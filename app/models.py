from pydantic import BaseModel, Field


class PersonalInfo(BaseModel):
    full_name: str | None = None
    email: str | None = None
    phone: str | None = None
    location: str | None = None
    date_of_birth: str | None = None
    gender: str | None = None
    nationality: str | None = None
    linkedin_url: str | None = None
    github_url: str | None = None
    personal_website: str | None = None


class Education(BaseModel):
    school: str | None = None
    degree: str | None = None
    field_of_study: str | None = None
    start_date: str | None = None
    end_date: str | None = None
    gpa: str | None = None
    description: str | None = None


class WorkExperience(BaseModel):
    company: str | None = None
    job_title: str | None = None
    employment_type: str | None = None
    location: str | None = None
    start_date: str | None = None
    end_date: str | None = None
    description: str | None = None


class Skill(BaseModel):
    name: str | None = None
    category: str | None = None


class Project(BaseModel):
    name: str | None = None
    description: str | None = None
    role: str | None = None
    start_date: str | None = None
    end_date: str | None = None
    url: str | None = None
    technologies: list[str] = Field(default_factory=list)


class Certification(BaseModel):
    name: str | None = None
    issuer: str | None = None
    issue_date: str | None = None
    expiration_date: str | None = None
    credential_id: str | None = None
    credential_url: str | None = None


class Award(BaseModel):
    title: str | None = None
    issuer: str | None = None
    date: str | None = None
    description: str | None = None


class Language(BaseModel):
    language: str | None = None
    proficiency: str | None = None


class Publication(BaseModel):
    title: str | None = None
    publisher: str | None = None
    date: str | None = None
    description: str | None = None
    url: str | None = None


class Reference(BaseModel):
    name: str | None = None
    position: str | None = None
    company: str | None = None
    contact_info: str | None = None


class VolunteerExperience(BaseModel):
    organization: str | None = None
    role: str | None = None
    start_date: str | None = None
    end_date: str | None = None
    description: str | None = None


class ExtracurricularActivity(BaseModel):
    name: str | None = None
    position: str | None = None
    start_date: str | None = None
    end_date: str | None = None
    description: str | None = None


class CVData(BaseModel):
    personal_info: PersonalInfo | None = None
    summary: str | None = None
    education: list[Education] = Field(default_factory=list)
    work_experience: list[WorkExperience] = Field(default_factory=list)
    skills: list[Skill] = Field(default_factory=list)
    projects: list[Project] = Field(default_factory=list)
    certifications: list[Certification] = Field(default_factory=list)
    awards: list[Award] = Field(default_factory=list)
    languages: list[Language] = Field(default_factory=list)
    publications: list[Publication] = Field(default_factory=list)
    references: list[Reference] = Field(default_factory=list)
    volunteer_experience: list[VolunteerExperience] = Field(default_factory=list)
    extracurricular_activities: list[ExtracurricularActivity] = Field(
        default_factory=list
    )


class ParseRequest(BaseModel):
    """Request model for parsing raw text"""

    text: str = Field(..., description="Raw CV/Resume text to parse")
    filename: str = Field(
        default="raw_text_input.txt", description="Optional filename for metadata"
    )
    multimodal: bool = Field(
        default=False,
        description="Use multimodal vision parsing (only for file uploads)",
    )
