import logging
import uuid
from datetime import datetime

from fastapi import (
    BackgroundTasks,
    FastAPI,
    File,
    Form,
    HTTPException,
    Request,
    UploadFile,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import HttpUrl

from app.api_dify import CallbackPayload, DifyCVParseResponse, send_callback
from app.api_dify import router as dify_router
from app.config import settings
from app.file_processors import FileProcessor, get_file_base64, process_file
from app.models import CVData, ParseRequest
from app.parser import CVParserState, cv_parser_workflow

# Set up logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


def transform_cvdata_to_v1_response(
    cv_data: CVData, request_id: str, status: str = "succeeded", cv_text: str = None
) -> DifyCVParseResponse:
    """Transform CVData to V1 DifyCVParseResponse format"""
    return DifyCVParseResponse(
        request_id=request_id,
        status=status,
        success=status == "succeeded",
        profile=cv_data.model_dump(),
        meta_data={
            "cv_text": cv_text,
            "parser_time": datetime.now().isoformat(),
        },
    )


async def process_cv_v2_with_callback(
    initial_state: CVParserState,
    callback_url: str,
    request_id: str,
    original_filename: str,
    cv_text: str = None,
) -> None:
    """Process CV with V2 parser and send callback with V1 response format"""
    try:
        # Run the CV parsing workflow
        result = await cv_parser_workflow.ainvoke(initial_state)

        # Check for errors
        if result.get("errors") and result["errors"]:
            error_msg = "; ".join(result["errors"])
            logger.error(f"[{request_id}] Parsing errors: {error_msg}")

            # Send error callback
            error_payload = CallbackPayload(
                request_id=request_id,
                status="failed",
                success=False,
                profile={},
                meta_data={
                    "cv_text": cv_text,
                    "parser_time": datetime.now().isoformat(),
                    "error": error_msg,
                },
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
            return

        if result.get("error"):
            logger.error(f"[{request_id}] Parsing error: {result['error']}")

            # Send error callback
            error_payload = CallbackPayload(
                request_id=request_id,
                status="failed",
                success=False,
                profile={},
                meta_data={
                    "cv_text": cv_text,
                    "parser_time": datetime.now().isoformat(),
                    "error": result["error"],
                },
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
            return

        # Get parsed data
        parsed_data = result.get("parsed_data")
        if not parsed_data:
            error_msg = "Failed to parse CV data"
            logger.error(f"[{request_id}] {error_msg}")

            # Send error callback
            error_payload = CallbackPayload(
                request_id=request_id,
                status="failed",
                success=False,
                profile={},
                meta_data={
                    "cv_text": cv_text,
                    "parser_time": datetime.now().isoformat(),
                    "error": error_msg,
                },
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
            return

        # Transform to V1 format and send callback
        v1_response = transform_cvdata_to_v1_response(
            parsed_data, request_id, "succeeded", cv_text
        )

        payload = CallbackPayload(
            request_id=request_id,
            status="succeeded",
            success=True,
            profile=v1_response.profile,
            meta_data=v1_response.meta_data,
            original_filename=original_filename,
        )

        await send_callback(callback_url, payload)

        logger.info(
            f"[{request_id}] Successfully processed CV with callback for: {parsed_data.personal_info.full_name if parsed_data.personal_info else 'Unknown'}"
        )

    except Exception as e:
        logger.error(f"[{request_id}] Error processing CV with callback: {str(e)}")

        # Send error callback
        error_payload = CallbackPayload(
            request_id=request_id,
            status="failed",
            success=False,
            profile={},
            meta_data={
                "cv_text": cv_text,
                "parser_time": datetime.now().isoformat(),
                "error": str(e),
            },
            original_filename=original_filename,
        )
        await send_callback(callback_url, error_payload)


# Create FastAPI app
app = FastAPI(
    title=settings.API_TITLE,
    version=settings.API_VERSION,
    description=settings.API_DESCRIPTION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(dify_router)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "CV Parser API",
        "version": settings.API_VERSION,
        "endpoints": {
            "parse": "/internal/v2/parse",
            "health": "/health",
            "dify_parse": "/internal/v1/parse",
            "mock_callback": "/mock/callback",
        },
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": settings.API_VERSION,
        "model": settings.OPENAI_MODEL,
    }


@app.post("/mock/callback")
async def mock_callback_endpoint(payload: CallbackPayload):
    """
    Mock callback endpoint for testing Dify CV parser callbacks.

    This endpoint simulates a webhook receiver that would process
    CV parsing results from the Dify parser.

    Use this URL as the callback_url when testing:
    http://localhost:8080/mock/callback
    """

    logger.info(f"Mock callback received for file: {payload.original_filename}")
    logger.info(f"Status: {payload.status}")
    logger.info(f"Success: {payload.success}")

    if payload.success:
        logger.info(f"Received mock callback payload: {payload.model_dump()}")
    else:
        logger.error(
            f"CV parsing failed: {payload.meta_data.get('error', 'Unknown error')}"
        )

    # Return success response
    import datetime

    return {
        "status": "received",
        "message": f"Mock callback processed for {payload.original_filename}",
        "received_at": datetime.datetime.now().isoformat(),
    }


@app.post("/internal/v2/parse", response_model=DifyCVParseResponse)
async def parse_cv(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile | None = File(
        None, description="CV file to parse (PDF, DOC, or DOCX)"
    ),
    multimodal: bool = Form(
        False, description="Use multimodal vision parsing for files"
    ),
    callback_url: HttpUrl | None = Form(
        None, description="Optional callback URL to receive results asynchronously"
    ),
) -> DifyCVParseResponse:
    """
    Parse CV/Resume using text extraction or multimodal vision parsing

    This endpoint accepts:
    1. File upload (PDF, DOC, or DOCX) with text extraction (default)
    2. File upload with multimodal vision parsing (set multimodal=true)
    3. Raw text in JSON body - parses directly

    Response format:
    Always returns DifyCVParseResponse format with structured CV data.
    If callback_url is provided, processing is done asynchronously.

    Args:
        file: CV file to parse
        multimodal: Use multimodal vision parsing for files
        callback_url: Optional URL to receive parsing results asynchronously

    Returns:
        DifyCVParseResponse with structured CV data
    """
    try:
        # Handle JSON request body for text parsing
        request_body = None
        if not file:
            # Try to parse JSON body
            content_type = request.headers.get("content-type", "").lower()
            if "application/json" in content_type:
                try:
                    body_bytes = await request.body()
                    if body_bytes:
                        import json

                        body_dict = json.loads(body_bytes.decode())
                        request_body = ParseRequest(**body_dict)
                except Exception as e:
                    logger.error(f"Failed to parse JSON body: {e}")
                    raise HTTPException(status_code=422, detail="Invalid JSON format")

        # Validate that at least one input is provided
        if not file and not request_body:
            raise HTTPException(
                status_code=400,
                detail="Either file upload or text in request body must be provided",
            )

        # Determine parsing mode
        use_multimodal = False
        if file:
            # File upload mode - check multimodal flag
            use_multimodal = multimodal
        elif request_body:
            # JSON mode - check multimodal in request body (only applies to future file uploads)
            use_multimodal = request_body.multimodal and file is not None

        # Check multimodal settings
        if use_multimodal and not settings.ENABLE_MULTIMODAL:
            raise HTTPException(
                status_code=403,
                detail="Multimodal parsing is disabled in configuration",
            )

        # Process input based on mode
        if file and use_multimodal:
            # Multimodal file processing
            logger.info(f"Received file for multimodal parsing: {file.filename}")

            # Validate file
            FileProcessor.validate_file(
                file, settings.ALLOWED_EXTENSIONS, settings.MAX_FILE_SIZE
            )

            # Get file as base64
            file_base64, mime_type = await get_file_base64(file)
            logger.info(f"Converted file to base64, size: {len(file_base64)} chars")

            # Create initial state for multimodal
            initial_state: CVParserState = {
                "input_text": None,
                "input_file_base64": file_base64,
                "filename": file.filename,
                "parsed_data": None,
                "error": None,
                "mode": "multimodal",
            }

        elif file:
            # Text extraction file processing
            logger.info(f"Received file: {file.filename}")
            text, filename = await process_file(
                file, settings.ALLOWED_EXTENSIONS, settings.MAX_FILE_SIZE
            )
            logger.info(f"Extracted {len(text)} characters from {filename}")

            # Create initial state for text
            initial_state: CVParserState = {
                "input_text": text,
                "input_file_base64": None,
                "filename": filename,
                "parsed_data": None,
                "error": None,
                "mode": "text",
            }

        else:
            # Raw text processing
            logger.info("Received raw text input")
            text = request_body.text
            filename = request_body.filename or "raw_text_input.txt"
            logger.info(f"Received {len(text)} characters of raw text")

            # Create initial state for text
            initial_state: CVParserState = {
                "input_text": text,
                "input_file_base64": None,
                "filename": filename,
                "parsed_data": None,
                "error": None,
                "mode": "text",
            }

        # Generate unique request ID for tracking
        request_id = str(uuid.uuid4())
        filename = initial_state.get("filename", "unknown")
        cv_text = initial_state.get("input_text", "")

        # Check if callback mode is requested
        if callback_url:
            logger.info(
                f"[{request_id}] Processing CV with callback for file: {filename} in mode: {initial_state.get('mode')}"
            )

            # Start background task for async processing
            background_tasks.add_task(
                process_cv_v2_with_callback,
                initial_state,
                str(callback_url),
                request_id,
                filename,
                cv_text,
            )

            # Return immediate response
            return DifyCVParseResponse(
                request_id=request_id,
                status="processing",
                success=True,
                profile={},
                meta_data={
                    "cv_text": cv_text,
                    "parser_time": datetime.now().isoformat(),
                },
            )

        else:
            # Synchronous processing
            logger.info(
                f"Starting CV parsing workflow for file: {filename} in mode: {initial_state.get('mode')}"
            )
            result = await cv_parser_workflow.ainvoke(initial_state)

            # Check for errors
            if result.get("errors") and result["errors"]:
                error_msg = "; ".join(result["errors"])
                logger.error(f"Parsing errors: {error_msg}")
                raise HTTPException(
                    status_code=400, detail=f"Parsing failed: {error_msg}"
                )

            if result.get("error"):
                logger.error(f"Parsing error: {result['error']}")
                raise HTTPException(status_code=400, detail=result["error"])

            # Return parsed data
            parsed_data = result.get("parsed_data")
            if not parsed_data:
                raise HTTPException(status_code=500, detail="Failed to parse CV data")

            mode_str = "multimodal" if use_multimodal else "text extraction"
            logger.info(
                f"Successfully parsed CV ({mode_str}) for: {parsed_data.personal_info.full_name if parsed_data.personal_info else 'Unknown'}"
            )

            # Return V1-compatible response format
            return transform_cvdata_to_v1_response(
                parsed_data, request_id, "succeeded", cv_text
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500, content={"error": "Internal server error", "status_code": 500}
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080)
