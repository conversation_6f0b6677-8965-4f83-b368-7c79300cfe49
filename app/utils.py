import logging

import phonenumbers
from phonenumbers import NumberParseException, PhoneNumberFormat

logger = logging.getLogger(__name__)


def format_vietnamese_phone_number(phone: str | None) -> str | None:
    """
    Format phone number to +84-xxxxxxxxx format for Vietnamese numbers using phonenumbers library.

    Args:
        phone: Raw phone number string

    Returns:
        Formatted phone number in +84-xxxxxxxxx format for Vietnamese numbers,
        or original format for international numbers, or None if invalid

    Examples:
        "0987654321" -> "+84-987654321" (valid Vietnamese mobile)
        "+84987654321" -> "+84-987654321"
        "84987654321" -> "+84-987654321"
        "******-123-4567" -> "******-123-4567" (unchanged)
        "0123456789" -> "+84-123456789" (fallback for invalid patterns)
    """
    if not phone:
        return None

    phone = phone.strip()
    if not phone:
        return None

    try:
        # First, try to parse as Vietnamese number (VN region)
        try:
            parsed_number = phonenumbers.parse(phone, "VN")
            if phonenumbers.is_valid_number(parsed_number):
                # Check if it's a Vietnamese number (country code 84)
                if parsed_number.country_code == 84:
                    # Format as international with custom format +84-xxxxxxxxx
                    formatted = phonenumbers.format_number(
                        parsed_number, PhoneNumberFormat.INTERNATIONAL
                    )
                    # Replace "+84 " with "+84-" and remove all spaces to match desired format
                    return formatted.replace("+84 ", "+84-").replace(" ", "")
                else:
                    # It's an international number, return in international format
                    return phonenumbers.format_number(
                        parsed_number, PhoneNumberFormat.INTERNATIONAL
                    )
        except NumberParseException:
            # If parsing with VN region fails, try without region
            pass

        # Try to parse without specifying region (for international numbers)
        try:
            parsed_number = phonenumbers.parse(phone, None)
            if phonenumbers.is_valid_number(parsed_number):
                if parsed_number.country_code == 84:
                    # Vietnamese number, format with custom format
                    formatted = phonenumbers.format_number(
                        parsed_number, PhoneNumberFormat.INTERNATIONAL
                    )
                    return formatted.replace("+84 ", "+84-").replace(" ", "")
                else:
                    # International number, keep standard international format
                    return phonenumbers.format_number(
                        parsed_number, PhoneNumberFormat.INTERNATIONAL
                    )
        except NumberParseException:
            pass

        # If phonenumbers library can't parse it, use fallback logic for Vietnamese-like patterns
        return _fallback_vietnamese_formatting(phone)

    except Exception as e:
        logger.debug(f"Error formatting phone number '{phone}': {str(e)}")
        return _fallback_vietnamese_formatting(phone)


def _fallback_vietnamese_formatting(phone: str) -> str:
    """
    Fallback formatting for Vietnamese-like phone numbers that phonenumbers library can't handle.
    This handles test cases and edge cases that might not be valid according to the library.
    """
    import re

    # Remove all non-digit characters except + at the beginning
    cleaned = re.sub(r"[^\d+]", "", phone.strip())

    if not cleaned:
        return phone

    # Handle different Vietnamese phone number patterns
    if cleaned.startswith("+84"):
        # Already has +84 prefix
        digits = cleaned[3:]
        if len(digits) == 9 or len(digits) == 10:
            # Remove leading 0 if present
            if digits.startswith("0"):
                digits = digits[1:]
            if len(digits) == 9:
                return f"+84-{digits}"
    elif cleaned.startswith("84") and not cleaned.startswith("+"):
        # Has 84 prefix without +
        digits = cleaned[2:]
        if len(digits) == 9 or len(digits) == 10:
            # Remove leading 0 if present
            if digits.startswith("0"):
                digits = digits[1:]
            if len(digits) == 9:
                return f"+84-{digits}"
    elif cleaned.startswith("0") and len(cleaned) == 10:
        # Vietnamese format starting with 0
        digits = cleaned[1:]  # Remove leading 0
        if len(digits) == 9:
            return f"+84-{digits}"
    elif not cleaned.startswith("+") and len(cleaned) == 9:
        # Assume Vietnamese if 9 digits without country code
        return f"+84-{cleaned}"
    elif cleaned.startswith("+") and not cleaned.startswith("+84"):
        # International number, keep original format but clean it up
        return phone.strip()

    # If none of the Vietnamese patterns match, return original
    logger.debug(
        f"Phone number '{phone}' doesn't match Vietnamese patterns, keeping original format"
    )
    return phone.strip()


def normalize_phone_number(phone: str | None) -> str | None:
    """
    Normalize phone number by applying Vietnamese formatting rules.
    This is the main function to be used for phone number normalization.

    Args:
        phone: Raw phone number string

    Returns:
        Normalized phone number
    """
    return format_vietnamese_phone_number(phone)
