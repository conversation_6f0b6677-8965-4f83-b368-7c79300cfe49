import json
import logging
import tempfile
import uuid
from pathlib import Path

import aiofiles
import aiofiles.os
import httpx
from fastapi import APIRouter, BackgroundTasks, File, Form, HTTPException, UploadFile
from pydantic import BaseModel, HttpUrl

from app.config import settings

# FastAPI Router and Models
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/internal/v1", tags=["dify-cv-parser"])


async def upload_file_to_dify(
    file_path: str | Path,
    timeout: float = 30.0,
) -> dict[str, any]:
    """
    Upload a file to Dify API.

    Args:
        file_path: Path to the file to upload
        api_token: Dify API token (Bearer token). If None, uses settings.DIFY_API_TOKEN
        base_url: Base URL for the Dify API. If None, uses settings.DIFY_BASE_URL
        timeout: Request timeout in seconds

    Returns:
        Dict containing the API response with file information

    Raises:
        httpx.HTTPStatusError: If the API request fails
        FileNotFoundError: If the file doesn't exist
        ValueError: If api_token is not provided and not set in config
    """
    # Use config defaults if not provided
    api_token = settings.DIFY_API_TOKEN
    base_url = settings.DIFY_BASE_URL

    if not api_token:
        raise ValueError(
            "Dify API token is required. Set DIFY_API_TOKEN in environment or pass api_token parameter."
        )

    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")

    url = f"{base_url}/v1/files/upload"
    headers = {"Authorization": f"Bearer {api_token}"}

    async with httpx.AsyncClient(timeout=timeout) as client:
        async with aiofiles.open(file_path, "rb") as file:
            file_content = await file.read()
            files = {"file": (file_path.name, file_content, "application/octet-stream")}

            response = await client.post(url=url, headers=headers, files=files)

            response.raise_for_status()
            return response.json()


async def cv_parse_dify(
    upload_file_id: str,
    api_token: str | None = None,
    user: str = "campaign_hiring",
    base_url: str | None = None,
    timeout: float = 60.0,
    response_mode: str = "streaming",
) -> dict[str, any]:
    """
    Parse a CV using Dify workflow API.

    Args:
        upload_file_id: ID of the uploaded file from upload_file_to_dify
        api_token: Dify API token (Bearer token). If None, uses settings.DIFY_API_TOKEN
        user: User identifier for the request
        base_url: Base URL for the Dify API. If None, uses settings.DIFY_BASE_URL
        timeout: Request timeout in seconds (default 60s for CV parsing)
        response_mode: Response mode ("blocking" or "streaming")

    Returns:
        Dict containing the workflow execution result with parsed CV data

    Raises:
        httpx.HTTPStatusError: If the API request fails
        ValueError: If api_token is not provided and not set in config
    """
    # Use config defaults if not provided
    if api_token is None:
        api_token = settings.DIFY_API_TOKEN
    if base_url is None:
        base_url = settings.DIFY_BASE_URL

    if not api_token:
        raise ValueError(
            "Dify API token is required. Set DIFY_API_TOKEN in environment or pass api_token parameter."
        )

    url = f"{base_url}/v1/workflows/run"
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json",
    }

    payload = {
        "inputs": {
            "cv_file": {
                "transfer_method": "local_file",
                "upload_file_id": upload_file_id,
                "type": "document",
            }
        },
        "response_mode": response_mode,
        "user": user,
    }

    if response_mode == "streaming":
        async with httpx.AsyncClient(timeout=timeout) as client:
            async with client.stream(
                "POST", url=url, headers=headers, json=payload
            ) as response:
                response.raise_for_status()

                # Process streaming messages to find the final result
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data_str = line[6:]  # Remove "data: " prefix
                            data = json.loads(data_str)

                            # Check if this is the workflow_finished event
                            if data.get("event") == "workflow_finished":
                                logger.info(
                                    f"Workflow finished: {data.get('workflow_run_id')}"
                                )
                                return (
                                    data  # Return the complete workflow_finished data
                                )

                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse streaming data: {e}")
                            continue

                raise ValueError(
                    "workflow_finished event not found in streaming response"
                )
    else:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url=url, headers=headers, json=payload)

            response.raise_for_status()
            return response.json()


class DifyCVParseResponse(BaseModel):
    """Response model for Dify CV parsing"""

    request_id: str
    status: str
    success: bool
    profile: dict
    meta_data: dict


class CallbackPayload(BaseModel):
    """Payload sent to callback URL"""

    request_id: str
    status: str
    success: bool
    profile: dict
    meta_data: dict
    original_filename: str


async def send_callback(callback_url: str, payload: CallbackPayload) -> None:
    """Send callback notification to the provided URL"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                callback_url,
                json=payload.model_dump(),
                headers={"Content-Type": "application/json"},
            )
            response.raise_for_status()
            logger.info(f"Callback sent successfully to {callback_url}")
    except Exception as e:
        logger.error(f"Failed to send callback to {callback_url}: {str(e)}")


async def process_cv_with_callback(
    upload_file_id: str, original_filename: str, callback_url: str, request_id: str
) -> None:
    """Process CV with streaming and send callback when complete"""
    try:
        # Continue processing the already started streaming workflow
        result = await cv_parse_dify(
            upload_file_id=upload_file_id, response_mode="blocking"
        )

        # Extract data from result (workflow_finished event structure)
        data = result.get("data", {})
        outputs = data.get("outputs", {})
        result_data = outputs.get("result", {})

        # Create callback payload
        payload = CallbackPayload(
            request_id=request_id,
            status=data.get("status", "unknown"),
            success=result_data.get("success", False),
            profile=result_data.get("profile", {}),
            meta_data=result_data.get("meta_data", {}),
            original_filename=original_filename,
        )

        # Send callback
        await send_callback(callback_url, payload)

    except Exception as e:
        logger.error(
            f"[{request_id}] Error processing streaming CV {original_filename}: {str(e)}"
        )

        # Send error callback
        error_payload = CallbackPayload(
            request_id=request_id,
            status="failed",
            success=False,
            profile={},
            meta_data={"error": str(e)},
            original_filename=original_filename,
        )
        await send_callback(callback_url, error_payload)


@router.post("/parse", response_model=DifyCVParseResponse)
async def parse_cv_dify(
    background_tasks: BackgroundTasks,
    file: UploadFile | None = File(
        None, description="CV file to parse (PDF, DOC, or DOCX)"
    ),
    callback_url: HttpUrl | None = Form(
        None, description="Optional callback URL to receive results"
    ),
) -> DifyCVParseResponse:
    """
    Parse CV using Dify API with optional callback notification.

    This endpoint:
    1. Accepts a CV file upload (PDF, DOC, or DOCX)
    2. Uploads the file to Dify API
    3. Processes the CV using Dify workflow
    4. Returns the parsed results immediately (if no callback URL)
    5. Optionally sends results to a callback URL (async processing)

    Configuration is read from environment variables:
    - DIFY_API_TOKEN: Dify API Bearer token
    - DIFY_BASE_URL: Dify API base URL

    Args:
        file: CV file to parse
        callback_url: Optional URL to receive parsing results

    Returns:
        Parsed CV data from Dify workflow
    """
    # Generate unique request ID for tracking
    request_id = str(uuid.uuid4())
    logger.info(
        f"Processing Dify CV request {request_id} for file: {file.filename if file else 'None'}"
    )

    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_ext} not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}",
            )

        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes",
            )

        # Reset file pointer
        await file.seek(0)

        logger.info(
            f"[{request_id}] Received CV file for Dify parsing: {file.filename}"
        )

        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
        temp_file_path = Path(temp_file.name)
        temp_file.close()

        # Write file content asynchronously
        async with aiofiles.open(temp_file_path, "wb") as f:
            await f.write(file_content)

        # First upload the file to get upload_file_id
        upload_result = await upload_file_to_dify(file_path=temp_file_path)
        upload_file_id = upload_result["id"]

        # If no callback URL, process synchronously (but still use streaming internally)
        if not callback_url:
            try:
                # Continue with full streaming processing
                result = await cv_parse_dify(
                    upload_file_id=upload_file_id, response_mode="blocking"
                )

                # Clean up temp file
                try:
                    await aiofiles.os.remove(temp_file_path)
                except FileNotFoundError:
                    pass  # File already deleted

                # Extract and return response data
                data = result.get("data", {})
                outputs = data.get("outputs", {})
                result_data = outputs.get("result", {})

                response = DifyCVParseResponse(
                    request_id=request_id,
                    status=data.get("status", "unknown"),
                    success=result_data.get("success", False),
                    profile=result_data.get("profile", {}),
                    meta_data=result_data.get("meta_data", {}),
                )

                logger.info(f"[{request_id}] Successfully parsed CV: {file.filename}")
                return response

            except Exception:
                # Clean up temp file on error
                try:
                    await aiofiles.os.remove(temp_file_path)
                except FileNotFoundError:
                    pass  # File already deleted
                raise

        else:
            # Process asynchronously with callback - workflow already started
            background_tasks.add_task(
                process_cv_with_callback,
                upload_file_id,
                file.filename,
                str(callback_url),
                request_id,
            )

            # Clean up temp file since we have the upload_file_id
            try:
                await aiofiles.os.remove(temp_file_path)
            except FileNotFoundError:
                pass  # File already deleted

            # Return immediate response with real IDs
            return DifyCVParseResponse(
                request_id=request_id,
                status="processing",
                success=True,
                profile={},
                meta_data={},
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing CV {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
