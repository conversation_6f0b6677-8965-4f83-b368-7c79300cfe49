import os

from dotenv import load_dotenv

load_dotenv()


class Settings:
    # OpenAI settings
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

    # API settings
    API_TITLE: str = "CV Parser API"
    API_VERSION: str = "1.2.1"
    API_DESCRIPTION: str = "API for parsing CV/Resume files using LangChain and OpenAI"

    # File upload settings
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".pdf", ".doc", ".docx"}

    # Parser settings
    PARSER_VERSION: str = "1.2.1"
    TEMPERATURE: float = 0.1  # Low temperature for consistent parsing
    MAX_RETRIES: int = 3

    # Multimodal settings
    ENABLE_MULTIMODAL: bool = True
    PDF_TO_IMAGE_DPI: int = int(
        os.getenv("PDF_TO_IMAGE_DPI", "200")
    )  # DPI for PDF to image conversion
    PDF_MAX_PAGES: int = int(
        os.getenv("PDF_MAX_PAGES", "10")
    )  # Max pages to process in multimodal mode
    PDF_IMAGE_FORMAT: str = os.getenv("PDF_IMAGE_FORMAT", "PNG")  # PNG or JPEG

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # Dify settings
    DIFY_BASE_URL: str = os.getenv(
        "DIFY_BASE_URL", "https://dify.xstaging.navigosgroup.site"
    )
    DIFY_API_TOKEN: str = os.getenv("DIFY_API_TOKEN", "")


settings = Settings()
