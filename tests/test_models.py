"""Tests for Pydantic models."""

import pytest
from pydantic import ValidationError

from app.models import (
    Award,
    Certification,
    CVData,
    Education,
    ExtracurricularActivity,
    Language,
    ParseRequest,
    PersonalInfo,
    Project,
    Publication,
    Reference,
    Skill,
    VolunteerExperience,
    WorkExperience,
)


class TestPersonalInfo:
    """Test PersonalInfo model."""

    def test_personal_info_valid_data(self):
        """Test PersonalInfo with valid data."""
        data = {
            "full_name": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "+84-123456789",
            "location": "San Francisco, CA",
            "linkedin_url": "https://linkedin.com/in/johndoe",
            "github_url": "https://github.com/johndoe",
        }
        personal_info = PersonalInfo(**data)
        assert personal_info.full_name == "<PERSON>"
        assert personal_info.email == "<EMAIL>"
        assert personal_info.phone == "+84-123456789"

    def test_personal_info_optional_fields(self):
        """Test PersonalInfo with only required fields."""
        personal_info = PersonalInfo()
        assert personal_info.full_name is None
        assert personal_info.email is None
        assert personal_info.phone is None

    def test_personal_info_partial_data(self):
        """Test PersonalInfo with partial data."""
        data = {"full_name": "Jane Doe", "email": "<EMAIL>"}
        personal_info = PersonalInfo(**data)
        assert personal_info.full_name == "Jane Doe"
        assert personal_info.email == "<EMAIL>"
        assert personal_info.phone is None

    def test_personal_info_special_characters(self):
        """Test PersonalInfo with special characters."""
        data = {
            "full_name": "José María O'Connor-Smith",
            "location": "München, Deutschland",
        }
        personal_info = PersonalInfo(**data)
        assert personal_info.full_name == "José María O'Connor-Smith"
        assert personal_info.location == "München, Deutschland"


class TestEducation:
    """Test Education model."""

    def test_education_valid_data(self):
        """Test Education with valid data."""
        data = {
            "school": "University of California, Berkeley",
            "degree": "Bachelor of Science",
            "field_of_study": "Computer Science",
            "start_date": "2014-09",
            "end_date": "2018-05",
            "gpa": "3.8/4.0",
        }
        education = Education(**data)
        assert education.school == "University of California, Berkeley"
        assert education.degree == "Bachelor of Science"
        assert education.gpa == "3.8/4.0"

    def test_education_minimal_data(self):
        """Test Education with minimal data."""
        education = Education(school="MIT")
        assert education.school == "MIT"
        assert education.degree is None

    def test_education_empty_data(self):
        """Test Education with no data."""
        education = Education()
        assert education.school is None
        assert education.degree is None


class TestWorkExperience:
    """Test WorkExperience model."""

    def test_work_experience_valid_data(self):
        """Test WorkExperience with valid data."""
        data = {
            "company": "Tech Corp",
            "job_title": "Senior Software Engineer",
            "employment_type": "Full-time",
            "location": "San Francisco, CA",
            "start_date": "2020-01",
            "end_date": "Present",
            "description": "Led development of scalable APIs",
        }
        work_exp = WorkExperience(**data)
        assert work_exp.company == "Tech Corp"
        assert work_exp.job_title == "Senior Software Engineer"
        assert work_exp.employment_type == "Full-time"

    def test_work_experience_ongoing_position(self):
        """Test WorkExperience for current position."""
        data = {
            "company": "Current Corp",
            "job_title": "Developer",
            "start_date": "2023-01",
            "end_date": None,
        }
        work_exp = WorkExperience(**data)
        assert work_exp.end_date is None


class TestSkill:
    """Test Skill model."""

    def test_skill_valid_data(self):
        """Test Skill with valid data."""
        skill = Skill(name="Python", category="Programming Languages")
        assert skill.name == "Python"
        assert skill.category == "Programming Languages"

    def test_skill_no_category(self):
        """Test Skill without category."""
        skill = Skill(name="Leadership")
        assert skill.name == "Leadership"
        assert skill.category is None


class TestProject:
    """Test Project model."""

    def test_project_valid_data(self):
        """Test Project with valid data."""
        data = {
            "name": "E-commerce Platform",
            "description": "Built full-stack application",
            "role": "Lead Developer",
            "start_date": "2022-01",
            "end_date": "2022-06",
            "url": "https://github.com/user/project",
            "technologies": ["Python", "React", "PostgreSQL"],
        }
        project = Project(**data)
        assert project.name == "E-commerce Platform"
        assert len(project.technologies) == 3
        assert "Python" in project.technologies

    def test_project_empty_technologies(self):
        """Test Project with empty technologies list."""
        project = Project(name="Simple Project")
        assert project.name == "Simple Project"
        assert project.technologies == []

    def test_project_no_url(self):
        """Test Project without URL."""
        project = Project(name="Internal Project", technologies=["Java"])
        assert project.url is None
        assert project.technologies == ["Java"]


class TestCertification:
    """Test Certification model."""

    def test_certification_valid_data(self):
        """Test Certification with valid data."""
        data = {
            "name": "AWS Certified Solutions Architect",
            "issuer": "Amazon Web Services",
            "issue_date": "2021-03",
            "expiration_date": "2024-03",
            "credential_id": "AWS-12345",
            "credential_url": "https://aws.amazon.com/verification",
        }
        cert = Certification(**data)
        assert cert.name == "AWS Certified Solutions Architect"
        assert cert.issuer == "Amazon Web Services"
        assert cert.credential_id == "AWS-12345"

    def test_certification_no_expiration(self):
        """Test Certification without expiration date."""
        cert = Certification(name="Lifetime Cert", issuer="Test Org")
        assert cert.expiration_date is None


class TestAward:
    """Test Award model."""

    def test_award_valid_data(self):
        """Test Award with valid data."""
        data = {
            "title": "Employee of the Year",
            "issuer": "Tech Corp",
            "date": "2022-12",
            "description": "Recognized for outstanding performance",
        }
        award = Award(**data)
        assert award.title == "Employee of the Year"
        assert award.issuer == "Tech Corp"


class TestLanguage:
    """Test Language model."""

    def test_language_valid_data(self):
        """Test Language with valid data."""
        language = Language(language="Spanish", proficiency="Fluent")
        assert language.language == "Spanish"
        assert language.proficiency == "Fluent"

    def test_language_no_proficiency(self):
        """Test Language without proficiency level."""
        language = Language(language="French")
        assert language.language == "French"
        assert language.proficiency is None


class TestPublication:
    """Test Publication model."""

    def test_publication_valid_data(self):
        """Test Publication with valid data."""
        data = {
            "title": "Machine Learning in Healthcare",
            "publisher": "IEEE",
            "date": "2023-01",
            "description": "Research on ML applications",
            "url": "https://ieeexplore.ieee.org/document/123456",
        }
        publication = Publication(**data)
        assert publication.title == "Machine Learning in Healthcare"
        assert publication.publisher == "IEEE"


class TestReference:
    """Test Reference model."""

    def test_reference_valid_data(self):
        """Test Reference with valid data."""
        data = {
            "name": "Alice Johnson",
            "position": "Senior Manager",
            "company": "Tech Corp",
            "contact_info": "<EMAIL>",
        }
        reference = Reference(**data)
        assert reference.name == "Alice Johnson"
        assert reference.position == "Senior Manager"


class TestVolunteerExperience:
    """Test VolunteerExperience model."""

    def test_volunteer_experience_valid_data(self):
        """Test VolunteerExperience with valid data."""
        data = {
            "organization": "Local Food Bank",
            "role": "Volunteer Coordinator",
            "start_date": "2020-01",
            "end_date": "2022-01",
            "description": "Organized volunteer activities",
        }
        volunteer = VolunteerExperience(**data)
        assert volunteer.organization == "Local Food Bank"
        assert volunteer.role == "Volunteer Coordinator"


class TestExtracurricularActivity:
    """Test ExtracurricularActivity model."""

    def test_extracurricular_activity_valid_data(self):
        """Test ExtracurricularActivity with valid data."""
        data = {
            "name": "Computer Science Club",
            "position": "President",
            "start_date": "2017-09",
            "end_date": "2018-05",
            "description": "Led programming workshops",
        }
        activity = ExtracurricularActivity(**data)
        assert activity.name == "Computer Science Club"
        assert activity.position == "President"


class TestCVData:
    """Test CVData main model."""

    def test_cv_data_empty(self):
        """Test CVData with no data."""
        cv_data = CVData()
        assert cv_data.personal_info is None
        assert cv_data.summary is None
        assert cv_data.education == []
        assert cv_data.work_experience == []
        assert cv_data.skills == []

    def test_cv_data_complete(self):
        """Test CVData with complete data."""
        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        education = [Education(school="MIT", degree="BS")]
        work_experience = [WorkExperience(company="Tech Corp", job_title="Engineer")]
        skills = [Skill(name="Python", category="Programming")]

        cv_data = CVData(
            personal_info=personal_info,
            summary="Software engineer with experience",
            education=education,
            work_experience=work_experience,
            skills=skills,
        )

        assert cv_data.personal_info.full_name == "John Doe"
        assert len(cv_data.education) == 1
        assert len(cv_data.work_experience) == 1
        assert len(cv_data.skills) == 1

    def test_cv_data_default_lists(self):
        """Test CVData default list initialization."""
        cv_data = CVData()

        # All list fields should be empty lists, not None
        assert isinstance(cv_data.education, list)
        assert isinstance(cv_data.work_experience, list)
        assert isinstance(cv_data.skills, list)
        assert isinstance(cv_data.projects, list)
        assert isinstance(cv_data.certifications, list)
        assert isinstance(cv_data.awards, list)
        assert isinstance(cv_data.languages, list)
        assert isinstance(cv_data.publications, list)
        assert isinstance(cv_data.references, list)
        assert isinstance(cv_data.volunteer_experience, list)
        assert isinstance(cv_data.extracurricular_activities, list)

    def test_cv_data_nested_validation(self):
        """Test CVData with invalid nested data."""
        with pytest.raises(ValidationError):
            CVData(personal_info="invalid_data")  # Should be PersonalInfo object

    def test_cv_data_serialization(self):
        """Test CVData serialization to dict."""
        cv_data = CVData(
            personal_info=PersonalInfo(full_name="John Doe"),
            summary="Test summary",
        )

        data_dict = cv_data.model_dump()
        assert data_dict["personal_info"]["full_name"] == "John Doe"
        assert data_dict["summary"] == "Test summary"
        assert isinstance(data_dict["education"], list)

    def test_cv_data_from_dict(self):
        """Test CVData creation from dictionary."""
        data = {
            "personal_info": {"full_name": "Jane Doe", "email": "<EMAIL>"},
            "summary": "Experienced developer",
            "education": [{"school": "Stanford", "degree": "MS"}],
            "skills": [{"name": "Python"}, {"name": "JavaScript"}],
        }

        cv_data = CVData(**data)
        assert cv_data.personal_info.full_name == "Jane Doe"
        assert cv_data.summary == "Experienced developer"
        assert len(cv_data.education) == 1
        assert len(cv_data.skills) == 2


class TestParseRequest:
    """Test ParseRequest model."""

    def test_parse_request_valid_data(self):
        """Test ParseRequest with valid data."""
        data = {
            "text": "John Doe\nSoftware Engineer",
            "filename": "resume.txt",
            "multimodal": False,
        }
        request = ParseRequest(**data)
        assert request.text == "John Doe\nSoftware Engineer"
        assert request.filename == "resume.txt"
        assert request.multimodal is False

    def test_parse_request_required_text(self):
        """Test ParseRequest requires text field."""
        with pytest.raises(ValidationError) as exc_info:
            ParseRequest(filename="test.txt")  # Missing required 'text' field

        assert "text" in str(exc_info.value)

    def test_parse_request_default_values(self):
        """Test ParseRequest default values."""
        request = ParseRequest(text="Test content")
        assert request.text == "Test content"
        assert request.filename == "raw_text_input.txt"  # Default value
        assert request.multimodal is False  # Default value

    def test_parse_request_multimodal_flag(self):
        """Test ParseRequest with multimodal flag."""
        request = ParseRequest(text="Test content", multimodal=True)
        assert request.multimodal is True

    def test_parse_request_empty_text(self):
        """Test ParseRequest with empty text."""
        request = ParseRequest(text="")
        assert request.text == ""

    def test_parse_request_long_text(self):
        """Test ParseRequest with very long text."""
        long_text = "A" * 100000  # 100k characters
        request = ParseRequest(text=long_text)
        assert len(request.text) == 100000

    def test_parse_request_special_characters(self):
        """Test ParseRequest with special characters in text and filename."""
        text = "José María\n職歴\n🎓 Education"
        filename = "résumé_josé.txt"
        request = ParseRequest(text=text, filename=filename)
        assert request.text == text
        assert request.filename == filename

    def test_parse_request_invalid_types(self):
        """Test ParseRequest with invalid data types."""
        with pytest.raises(ValidationError):
            ParseRequest(text=123)  # text should be string

        with pytest.raises(ValidationError):
            ParseRequest(
                text="valid", multimodal="invalid"
            )  # multimodal should be bool

    def test_parse_request_none_filename(self):
        """Test ParseRequest with None filename (should use default)."""
        request = ParseRequest(text="Test")
        assert request.filename == "raw_text_input.txt"

    def test_parse_request_serialization(self):
        """Test ParseRequest serialization."""
        request = ParseRequest(
            text="Test content", filename="test.txt", multimodal=True
        )

        data = request.model_dump()
        assert data["text"] == "Test content"
        assert data["filename"] == "test.txt"
        assert data["multimodal"] is True

    def test_parse_request_from_json(self):
        """Test ParseRequest creation from JSON-like dict."""
        json_data = {
            "text": "John Doe\nDeveloper",
            "filename": "cv.txt",
            "multimodal": False,
        }

        request = ParseRequest(**json_data)
        assert request.text == "John Doe\nDeveloper"
        assert request.filename == "cv.txt"
        assert request.multimodal is False


class TestModelIntegration:
    """Test model integration and relationships."""

    def test_complete_cv_model_integration(self):
        """Test integration of all models in a complete CV."""
        # Create a complete CV with all possible fields
        cv_data = CVData(
            personal_info=PersonalInfo(
                full_name="Dr. Maria García-López",
                email="<EMAIL>",
                phone="+34-123-456-789",
                location="Barcelona, Spain",
                linkedin_url="https://linkedin.com/in/mariagarcia",
            ),
            summary="Multilingual software architect with 10+ years experience",
            education=[
                Education(
                    school="Universidad Politécnica de Madrid",
                    degree="PhD in Computer Science",
                    field_of_study="Artificial Intelligence",
                    start_date="2008-09",
                    end_date="2012-06",
                    gpa="9.2/10",
                ),
                Education(
                    school="MIT",
                    degree="Master of Science",
                    field_of_study="Computer Science",
                    start_date="2006-09",
                    end_date="2008-06",
                ),
            ],
            work_experience=[
                WorkExperience(
                    company="Google",
                    job_title="Senior Software Architect",
                    employment_type="Full-time",
                    location="Mountain View, CA",
                    start_date="2018-01",
                    description="Led architecture for ML platform serving 1B+ users",
                ),
            ],
            skills=[
                Skill(name="Python", category="Programming Languages"),
                Skill(name="Machine Learning", category="Technical Skills"),
                Skill(name="Leadership", category="Soft Skills"),
            ],
            projects=[
                Project(
                    name="AI-Powered Recommendation System",
                    description="Built recommendation engine for e-commerce",
                    role="Technical Lead",
                    technologies=["Python", "TensorFlow", "Kubernetes"],
                    url="https://github.com/maria/recommender",
                ),
            ],
            certifications=[
                Certification(
                    name="Google Cloud Professional Architect",
                    issuer="Google Cloud",
                    issue_date="2020-03",
                    expiration_date="2023-03",
                ),
            ],
            awards=[
                Award(
                    title="Innovation Award",
                    issuer="Tech Conference 2022",
                    date="2022-11",
                    description="Best AI Innovation",
                ),
            ],
            languages=[
                Language(language="Spanish", proficiency="Native"),
                Language(language="English", proficiency="Fluent"),
                Language(language="Catalan", proficiency="Fluent"),
            ],
            publications=[
                Publication(
                    title="Deep Learning for Recommendation Systems",
                    publisher="ACM",
                    date="2021-05",
                    url="https://dl.acm.org/doi/10.1145/123456",
                ),
            ],
            references=[
                Reference(
                    name="Dr. John Smith",
                    position="Research Director",
                    company="Google Research",
                    contact_info="<EMAIL>",
                ),
            ],
            volunteer_experience=[
                VolunteerExperience(
                    organization="Girls Who Code",
                    role="Mentor",
                    start_date="2019-01",
                    description="Mentoring young women in tech",
                ),
            ],
            extracurricular_activities=[
                ExtracurricularActivity(
                    name="AI Research Group",
                    position="Founding Member",
                    start_date="2010-01",
                    end_date="2012-06",
                ),
            ],
        )

        # Verify the complete model works
        assert cv_data.personal_info.full_name == "Dr. Maria García-López"
        assert len(cv_data.education) == 2
        assert len(cv_data.work_experience) == 1
        assert len(cv_data.skills) == 3
        assert len(cv_data.projects) == 1
        assert len(cv_data.languages) == 3
        # Note: metadata field was removed from CVData model

        # Test serialization of complete model
        data_dict = cv_data.model_dump()
        assert isinstance(data_dict, dict)
        assert "personal_info" in data_dict
        assert "education" in data_dict
        assert len(data_dict["languages"]) == 3

        # Test deserialization
        cv_data_restored = CVData(**data_dict)
        assert (
            cv_data_restored.personal_info.full_name == cv_data.personal_info.full_name
        )
        assert len(cv_data_restored.education) == len(cv_data.education)
