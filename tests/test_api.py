"""API endpoint tests for CV Parser API."""

from io import BytesIO
from unittest.mock import AsyncMock, patch

from fastapi.testclient import TestClient

from app.models import CVData


class TestRootEndpoint:
    """Test the root endpoint."""

    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint returns correct information."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "CV Parser API"
        assert data["version"] == "1.2.1"
        assert "endpoints" in data
        assert data["endpoints"]["parse"] == "/internal/v2/parse"
        assert data["endpoints"]["health"] == "/health"

    def test_root_endpoint_structure(self, client: TestClient):
        """Test root endpoint response structure."""
        response = client.get("/")
        data = response.json()
        required_fields = ["message", "version", "endpoints"]
        for field in required_fields:
            assert field in data


class TestHealthEndpoint:
    """Test the health check endpoint."""

    def test_health_check(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["version"] == "1.2.1"
        assert data["model"] == "gpt-4o-mini"

    def test_health_endpoint_structure(self, client: TestClient):
        """Test health endpoint response structure."""
        response = client.get("/health")
        data = response.json()
        required_fields = ["status", "version", "model"]
        for field in required_fields:
            assert field in data


class TestParseEndpoint:
    """Test the main parse endpoint."""

    @patch("app.api.cv_parser_workflow")
    @patch("app.api.process_file")
    def test_file_upload_text_extraction(
        self,
        mock_process_file,
        mock_workflow,
        client: TestClient,
        sample_parsed_cv: CVData,
    ):
        """Test file upload with text extraction."""
        # Setup mocks
        mock_process_file.return_value = ("Sample CV text", "test.pdf")
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create test file
        file_content = b"fake pdf content"
        files = {"file": ("test.pdf", BytesIO(file_content), "application/pdf")}

        response = client.post("/internal/v2/parse", files=files)

        assert response.status_code == 200
        data = response.json()
        
        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data
        
        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"
        assert profile["personal_info"]["email"] == "<EMAIL>"
        
        # Check meta_data structure
        assert "cv_text" in data["meta_data"]
        assert "parser_time" in data["meta_data"]
        assert data["meta_data"]["cv_text"] == "Sample CV text"
        
        mock_process_file.assert_called_once()

    @patch("app.api.cv_parser_workflow")
    @patch("app.api.FileProcessor.validate_file")
    @patch("app.api.get_file_base64")
    def test_file_upload_multimodal(
        self,
        mock_get_base64,
        mock_validate,
        mock_workflow,
        client: TestClient,
        sample_parsed_cv: CVData,
    ):
        """Test file upload with multimodal parsing."""
        # Setup mocks
        mock_get_base64.return_value = ("base64content", "application/pdf")
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create test file
        file_content = b"fake pdf content"
        files = {
            "file": ("test.pdf", BytesIO(file_content), "application/pdf"),
            "multimodal": (None, "true"),
        }

        response = client.post("/internal/v2/parse", files=files)

        assert response.status_code == 200
        data = response.json()
        
        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data
        
        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"
        
        # Check meta_data structure
        assert "cv_text" in data["meta_data"]
        assert "parser_time" in data["meta_data"]
        
        mock_validate.assert_called_once()
        mock_get_base64.assert_called_once()

    @patch("app.api.cv_parser_workflow")
    def test_raw_text_parsing(
        self,
        mock_workflow,
        client: TestClient,
        sample_parsed_cv: CVData,
        sample_cv_text: str,
    ):
        """Test raw text parsing."""
        # Setup mock
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Test data
        request_data = {"text": sample_cv_text, "filename": "test.txt"}

        response = client.post("/internal/v2/parse", json=request_data)

        assert response.status_code == 200
        data = response.json()
        
        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data
        
        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"
        
        # Check meta_data structure
        assert "cv_text" in data["meta_data"]
        assert "parser_time" in data["meta_data"]
        assert data["meta_data"]["cv_text"] == sample_cv_text

    def test_no_input_provided(self, client: TestClient):
        """Test error when no input is provided."""
        response = client.post("/internal/v2/parse")
        assert response.status_code == 400
        assert (
            "Either file upload or text in request body must be provided"
            in response.text
        )

    def test_invalid_file_type(self, client: TestClient):
        """Test error with invalid file type."""
        file_content = b"text content"
        files = {"file": ("test.txt", BytesIO(file_content), "text/plain")}

        with patch("app.api.process_file") as mock_process:
            mock_process.side_effect = Exception("Invalid file type")
            response = client.post("/internal/v2/parse", files=files)
            assert response.status_code == 500

    def test_file_too_large(self, client: TestClient, large_file_content: bytes):
        """Test error when file is too large."""
        files = {"file": ("large.pdf", BytesIO(large_file_content), "application/pdf")}

        with patch("app.api.process_file") as mock_process:
            mock_process.side_effect = Exception("File too large")
            response = client.post("/internal/v2/parse", files=files)
            assert response.status_code == 500

    def test_multimodal_disabled(self, client: TestClient):
        """Test multimodal parsing when disabled in settings."""
        with patch("app.config.settings.ENABLE_MULTIMODAL", False):
            file_content = b"fake pdf content"
            files = {
                "file": ("test.pdf", BytesIO(file_content), "application/pdf"),
                "multimodal": (None, "true"),
            }

            response = client.post("/internal/v2/parse", files=files)
            assert response.status_code == 403
            assert "Multimodal parsing is disabled" in response.text

    @patch("app.api.cv_parser_workflow")
    def test_parsing_error_in_workflow(self, mock_workflow, client: TestClient):
        """Test handling of parsing errors in workflow."""
        # Setup mock to return error
        mock_workflow.ainvoke = AsyncMock(return_value={"error": "Parsing failed"})

        request_data = {"text": "John Doe\nSoftware Engineer"}
        response = client.post("/internal/v2/parse", json=request_data)

        assert response.status_code == 400
        assert "Parsing failed" in response.text

    @patch("app.api.cv_parser_workflow")
    def test_workflow_returns_no_data(self, mock_workflow, client: TestClient):
        """Test handling when workflow returns no parsed data."""
        # Setup mock to return empty result
        mock_workflow.ainvoke = AsyncMock(return_value={"parsed_data": None})

        request_data = {"text": "John Doe\nSoftware Engineer"}
        response = client.post("/internal/v2/parse", json=request_data)

        assert response.status_code == 500
        assert "Failed to parse CV data" in response.text

    def test_malformed_json(self, client: TestClient):
        """Test handling of malformed JSON."""
        response = client.post(
            "/internal/v2/parse",
            content='{"text": "John Doe", invalid json}',
            headers={"Content-Type": "application/json"},
        )
        assert response.status_code == 422  # Unprocessable Entity

    def test_missing_required_text_field(self, client: TestClient):
        """Test error when required text field is missing."""
        request_data = {"filename": "test.txt"}  # Missing 'text' field
        response = client.post("/internal/v2/parse", json=request_data)
        assert response.status_code == 422

    def test_empty_text_input(self, client: TestClient):
        """Test handling of empty text input."""
        request_data = {"text": ""}

        with patch("app.api.cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"error": "No text provided for text mode parsing"}
            )
            response = client.post("/internal/v2/parse", json=request_data)
            assert response.status_code == 400

    def test_very_long_text_input(self, client: TestClient, sample_parsed_cv: CVData):
        """Test handling of very long text input."""
        long_text = "John Doe\nSoftware Engineer\n" * 10000  # Very long text
        request_data = {"text": long_text}

        with patch("app.api.cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"parsed_data": sample_parsed_cv}
            )
            response = client.post("/internal/v2/parse", json=request_data)
            assert response.status_code == 200

    def test_special_characters_in_filename(
        self, client: TestClient, sample_parsed_cv: CVData
    ):
        """Test handling of special characters in filename."""
        request_data = {"text": "John Doe", "filename": "résumé-ñoël.txt"}

        with patch("app.api.cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"parsed_data": sample_parsed_cv}
            )
            response = client.post("/internal/v2/parse", json=request_data)
            assert response.status_code == 200

    @patch("app.api.cv_parser_workflow")
    @patch("app.api.process_file")
    def test_file_with_empty_content(
        self, mock_process_file, mock_workflow, client: TestClient
    ):
        """Test handling of file with no extractable content."""
        # Setup mock to return empty text
        mock_process_file.return_value = ("", "empty.pdf")
        mock_workflow.ainvoke = AsyncMock(
            return_value={
                "error": "Could not extract basic personal information from CV"
            }
        )

        file_content = b"fake pdf content"
        files = {"file": ("empty.pdf", BytesIO(file_content), "application/pdf")}

        response = client.post("/internal/v2/parse", files=files)
        assert response.status_code == 400

    def test_concurrent_requests(self, client: TestClient, sample_parsed_cv: CVData):
        """Test handling of concurrent requests."""
        import threading

        results = []

        def make_request():
            request_data = {"text": "John Doe\nSoftware Engineer"}
            with patch("app.api.cv_parser_workflow") as mock_workflow:
                mock_workflow.ainvoke = AsyncMock(
                    return_value={"parsed_data": sample_parsed_cv}
                )
                response = client.post("/internal/v2/parse", json=request_data)
                results.append(response.status_code)

        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        assert all(status == 200 for status in results)


class TestErrorHandling:
    """Test error handling across the API."""

    def test_http_exception_handler(self, client: TestClient):
        """Test custom HTTP exception handler."""
        # Trigger a known 400 error
        response = client.post("/internal/v2/parse")
        assert response.status_code == 400
        data = response.json()
        assert "error" in data
        assert "status_code" in data
        assert data["status_code"] == 400

    def test_general_exception_handler(self, client: TestClient):
        """Test general exception handler."""
        # Test with a scenario that causes a general exception in the API layer
        with patch("app.api.process_file") as mock_process_file:
            # Mock to raise a RuntimeError that should be caught by general exception handler
            mock_process_file.side_effect = RuntimeError("Unexpected error")

            file_content = b"fake pdf content"
            files = {"file": ("test.pdf", BytesIO(file_content), "application/pdf")}
            response = client.post("/internal/v2/parse", files=files)

            # This RuntimeError should be caught by the general exception handler
            assert response.status_code == 500
            data = response.json()
            assert data["error"] == "Internal server error"
            assert data["status_code"] == 500

    def test_validation_error_handling(self, client: TestClient):
        """Test Pydantic validation error handling."""
        # Send invalid data type
        response = client.post(
            "/internal/v2/parse",
            json={"text": 123},  # text should be string, not int
        )
        assert response.status_code == 422


class TestAuthentication:
    """Test authentication and authorization scenarios."""

    @patch("app.config.settings.OPENAI_API_KEY", "")
    def test_missing_api_key(self, client: TestClient):
        """Test behavior when OpenAI API key is missing."""
        request_data = {"text": "John Doe"}

        with patch("app.api.cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                side_effect=Exception("OpenAI API key not provided")
            )
            response = client.post("/internal/v2/parse", json=request_data)
            assert response.status_code == 500
