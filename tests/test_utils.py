from unittest.mock import AsyncMock, patch

import pytest

from app.models import PersonalInfo
from app.parser import PersonalInfoPars<PERSON>, PersonalInfoWithSummary
from app.utils import format_vietnamese_phone_number, normalize_phone_number


class TestPhoneNumberFormatting:
    """Test phone number formatting utilities."""

    def test_format_vietnamese_phone_number_with_leading_zero(self):
        """Test formatting Vietnamese phone number starting with 0."""
        result = format_vietnamese_phone_number("0123456789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_with_plus_84(self):
        """Test formatting phone number already with +84."""
        result = format_vietnamese_phone_number("+84123456789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_with_84_prefix(self):
        """Test formatting phone number with 84 prefix (no plus)."""
        result = format_vietnamese_phone_number("84123456789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_nine_digits(self):
        """Test formatting 9-digit Vietnamese phone number."""
        result = format_vietnamese_phone_number("123456789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_with_spaces_and_dashes(self):
        """Test formatting phone number with spaces and dashes."""
        result = format_vietnamese_phone_number("0123-456-789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_with_plus_84_and_leading_zero(self):
        """Test formatting phone number with +84 and leading zero."""
        result = format_vietnamese_phone_number("+840123456789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_international_unchanged(self):
        """Test that international numbers remain unchanged."""
        result = format_vietnamese_phone_number("******-123-4567")
        assert result == "******-123-4567"

    def test_format_vietnamese_phone_number_none_input(self):
        """Test handling None input."""
        result = format_vietnamese_phone_number(None)
        assert result is None

    def test_format_vietnamese_phone_number_empty_string(self):
        """Test handling empty string."""
        result = format_vietnamese_phone_number("")
        assert result is None

    def test_format_vietnamese_phone_number_invalid_format(self):
        """Test handling invalid phone number format."""
        result = format_vietnamese_phone_number("abc123")
        assert result == "abc123"  # Returns original if doesn't match patterns

    def test_format_vietnamese_phone_number_too_short(self):
        """Test handling phone number that's too short."""
        result = format_vietnamese_phone_number("12345")
        assert result == "12345"  # Returns original if doesn't match patterns

    def test_format_vietnamese_phone_number_too_long(self):
        """Test handling phone number that's too long."""
        result = format_vietnamese_phone_number("012345678901")
        assert result == "012345678901"  # Returns original if doesn't match patterns

    def test_format_vietnamese_phone_number_with_parentheses(self):
        """Test formatting phone number with parentheses."""
        result = format_vietnamese_phone_number("(0123) 456-789")
        assert result == "+84-123456789"

    def test_format_vietnamese_phone_number_with_dots(self):
        """Test formatting phone number with dots."""
        result = format_vietnamese_phone_number("0123.456.789")
        assert result == "+84-123456789"

    def test_normalize_phone_number_delegates_to_format_function(self):
        """Test that normalize_phone_number delegates to format_vietnamese_phone_number."""
        test_number = "0123456789"
        result = normalize_phone_number(test_number)
        expected = format_vietnamese_phone_number(test_number)
        assert result == expected

    def test_normalize_phone_number_none_input(self):
        """Test normalize_phone_number with None input."""
        result = normalize_phone_number(None)
        assert result is None

    @pytest.mark.parametrize(
        "input_phone,expected",
        [
            ("0123456789", "+84-123456789"),
            ("+84123456789", "+84-123456789"),
            ("84123456789", "+84-123456789"),
            ("123456789", "+84-123456789"),
            ("******-123-4567", "******-123-4567"),
            ("0123-456-789", "+84-123456789"),
            ("(0123) 456 789", "+84-123456789"),
            ("+84 0123 456 789", "+84-123456789"),
            (None, None),
            ("", None),
        ],
    )
    def test_phone_number_formatting_parametrized(self, input_phone, expected):
        """Parametrized test for various phone number formats."""
        result = format_vietnamese_phone_number(input_phone)
        assert result == expected


class TestParserPhoneNumberIntegration:
    """Test phone number formatting integration with parser."""

    @pytest.mark.asyncio
    async def test_personal_info_parser_formats_phone_number(self):
        """Test that PersonalInfoParser formats phone numbers correctly."""
        # Mock the LLM response
        mock_result = PersonalInfoWithSummary(
            personal_info=PersonalInfo(
                full_name="John Doe",
                email="<EMAIL>",
                phone="0123456789",  # Vietnamese format with leading zero
            ),
            summary="Test summary",
        )

        parser = PersonalInfoParser()

        # Mock the chain to return our test data
        with patch.object(parser, "create_chain") as mock_create_chain:
            mock_chain = AsyncMock()
            mock_chain.ainvoke.return_value = mock_result
            mock_create_chain.return_value = mock_chain

            # Call the parser
            result = await parser.parse_from_text("Sample CV text")

            # Verify phone number was formatted correctly
            assert result.personal_info.phone == "+84-123456789"
            assert result.personal_info.full_name == "John Doe"
            assert result.personal_info.email == "<EMAIL>"
            assert result.summary == "Test summary"

    @pytest.mark.asyncio
    async def test_personal_info_parser_handles_none_phone(self):
        """Test that PersonalInfoParser handles None phone number."""
        # Mock the LLM response with no phone
        mock_result = PersonalInfoWithSummary(
            personal_info=PersonalInfo(
                full_name="Jane Doe", email="<EMAIL>", phone=None
            ),
            summary="Test summary",
        )

        parser = PersonalInfoParser()

        # Mock the chain to return our test data
        with patch.object(parser, "create_chain") as mock_create_chain:
            mock_chain = AsyncMock()
            mock_chain.ainvoke.return_value = mock_result
            mock_create_chain.return_value = mock_chain

            # Call the parser
            result = await parser.parse_from_text("Sample CV text")

            # Verify phone number remains None
            assert result.personal_info.phone is None
            assert result.personal_info.full_name == "Jane Doe"

    @pytest.mark.asyncio
    async def test_personal_info_parser_handles_international_phone(self):
        """Test that PersonalInfoParser preserves international phone numbers."""
        # Mock the LLM response with international phone
        mock_result = PersonalInfoWithSummary(
            personal_info=PersonalInfo(
                full_name="Bob Smith",
                email="<EMAIL>",
                phone="******-123-4567",  # US format
            ),
            summary="Test summary",
        )

        parser = PersonalInfoParser()

        # Mock the chain to return our test data
        with patch.object(parser, "create_chain") as mock_create_chain:
            mock_chain = AsyncMock()
            mock_chain.ainvoke.return_value = mock_result
            mock_create_chain.return_value = mock_chain

            # Call the parser
            result = await parser.parse_from_text("Sample CV text")

            # Verify international phone number is preserved
            assert result.personal_info.phone == "******-123-4567"
            assert result.personal_info.full_name == "Bob Smith"

    @pytest.mark.asyncio
    async def test_personal_info_parser_formats_realistic_vietnamese_phone(self):
        """Test that PersonalInfoParser formats realistic Vietnamese phone numbers using phonenumbers library."""
        # Mock the LLM response with realistic Vietnamese phone
        mock_result = PersonalInfoWithSummary(
            personal_info=PersonalInfo(
                full_name="Nguyen Van A",
                email="<EMAIL>",
                phone="0987654321",  # Realistic Vietnamese mobile number
            ),
            summary="Test summary",
        )

        parser = PersonalInfoParser()

        # Mock the chain to return our test data
        with patch.object(parser, "create_chain") as mock_create_chain:
            mock_chain = AsyncMock()
            mock_chain.ainvoke.return_value = mock_result
            mock_create_chain.return_value = mock_chain

            # Call the parser
            result = await parser.parse_from_text("Sample CV text")

            # Verify realistic Vietnamese phone number was formatted correctly using phonenumbers library
            assert result.personal_info.phone == "+84-987654321"
            assert result.personal_info.full_name == "Nguyen Van A"

    @pytest.mark.asyncio
    async def test_personal_info_parser_formats_vietnamese_phone_with_spaces(self):
        """Test that PersonalInfoParser formats Vietnamese phone numbers with spaces."""
        # Mock the LLM response with Vietnamese phone with spaces
        mock_result = PersonalInfoWithSummary(
            personal_info=PersonalInfo(
                full_name="Tran Thi B",
                email="<EMAIL>",
                phone="+84 912 345 678",  # Vietnamese with spaces
            ),
            summary="Test summary",
        )

        parser = PersonalInfoParser()

        # Mock the chain to return our test data
        with patch.object(parser, "create_chain") as mock_create_chain:
            mock_chain = AsyncMock()
            mock_chain.ainvoke.return_value = mock_result
            mock_create_chain.return_value = mock_chain

            # Call the parser
            result = await parser.parse_from_text("Sample CV text")

            # Verify Vietnamese phone number with spaces was formatted correctly
            assert result.personal_info.phone == "+84-912345678"
            assert result.personal_info.full_name == "Tran Thi B"
